import { getRedisClient } from '../redisClient.js';

export class MessageRetriever {
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
		this.MESSAGE_HISTORY_LENGTH = 10;
	}

	async getMessages(chatId, botUsername) {
		const key = this._buildKey(chatId, botUsername);
		
		try {
			const messages = await this.redis.get(key);
			return messages 
				? this._filterMessages(messages)
				: [];
		} catch (error) {
			console.error(`Error retrieving previous messages from Red<PERSON> for chat ${chatId}: ${error.message}`);
			return [];
		}
	}

	_buildKey(chatId, botUsername) {
		const username = botUsername || this.env.TELEGRAM_BOT_USERNAME;
		return `${username}:chat:${chatId.toString()}:messages`;
	}

	_filterMessages(messages) {
		return messages
			.filter(m => m.text && m.text.trim() !== '')
			.slice(-this.MESSAGE_HISTORY_LENGTH);
	}
}