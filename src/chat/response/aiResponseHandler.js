/**
 * Handles AI response processing and delivery
 * @module aiResponseHandler
 */

import { getVectorStoreIndex } from '../../vectorStore.js';
import { generateEmbedding } from '../geminiAI.js';
import { logTelegramMessage } from '../../redis.js';

/**
 * <PERSON>les logging, escaping, and sending the AI response back to Telegram
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string|Array} aiResponseText - The AI response text (can be an array of responses)
 * @param {string|number} originalMessageId - The ID of the original message being responded to
 * @param {Array} [aiResponseThoughts] - Optional array of AI thoughts
 * @returns {Promise<void>}
 */
export async function handle(env, chatId, aiResponseText, originalMessageId, aiResponseThoughts = []) {
	// Import here to avoid circular dependencies
	const { escapeMdV2 } = await import('../telegramUtils.js');
	const { sendLongTelegramMessage } = await import('../../telegram.js');

	try {
		// Handle array of responses: use only the last response if array
		const responseText = _extractResponseText(aiResponseText);

		// Log thoughts if present
		if (aiResponseThoughts?.length > 0) {
			console.log('AI Thoughts:', aiResponseThoughts);
		}

		// Log and store the response
		await _logAndStoreResponse(env, chatId, responseText, originalMessageId, aiResponseThoughts);

		// Send the response to the user
		await _sendResponse(env, chatId, responseText, originalMessageId, sendLongTelegramMessage, escapeMdV2);
	} catch (error) {
		console.error('Error in handleAIResponse:', error);
		throw error;
	}
}

/**
 * Extracts response text from possible response formats
 * @private
 * @param {string|Array|Object} response - The AI response
 * @returns {string} Extracted text
 */
function _extractResponseText(response) {
	if (!response) return '';

	if (Array.isArray(response)) {
		const last = response[response.length - 1];
		return last?.text.trim() || '';
	}

	return typeof response === 'string' ? response.trim() : '';
}

/**
 * Logs the AI response to Redis and stores its embedding
 * @private
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string} responseText - The response text to log and store
 * @param {string|number} originalMessageId - The original message ID
 * @param {Array} thoughts - AI thoughts
 * @returns {Promise<void>}
 */
async function _logAndStoreResponse(env, chatId, responseText, originalMessageId, thoughts = []) {
	const aiMessageTimestamp = Date.now() / 1000;

	// Log to Redis
	logTelegramMessage(env, chatId, {
		text: responseText,
		thoughts,
		from: {
			username: env.TELEGRAM_BOT_NAME,
			is_bot: true,
		},
		date: aiMessageTimestamp,
	});

	// Store embedding if response is not empty
	if (!responseText) return;

	try {
		const vectorIndex = getVectorStoreIndex(env);
		const aiMessageEmbedding = await generateEmbedding(env, responseText);

		if (!aiMessageEmbedding) {
			console.warn('Could not generate embedding for AI response.');
			return;
		}

		const aiMessageId = `${originalMessageId}_ai_response`;
		await vectorIndex.upsert({
			id: aiMessageId,
			vector: aiMessageEmbedding,
			metadata: {
				text: responseText,
				chatId,
				userId: null, // Or env.TELEGRAM_BOT_ID if available
				role: 'assistant',
				timestamp: aiMessageTimestamp,
				replyToMessageId: originalMessageId.toString(),
			},
		});

		console.log(`AI response for message ${originalMessageId} embedded and stored.`);
	} catch (error) {
		console.error('Error storing AI response embedding:', error);
		// Continue even if embedding fails
	}
}

/**
 * Sends the AI response to the user
 * @private
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID
 * @param {string} responseText - The response text to send
 * @param {string|number} originalMessageId - The original message ID
 * @param {Function} sendMessage - Function to send the message
 * @param {Function} escapeFn - Function to escape markdown
 * @returns {Promise<void>}
 */
async function _sendResponse(env, chatId, responseText, originalMessageId, sendMessage, escapeFn) {
	if (!responseText) {
		console.warn('Empty response text, nothing to send');
		return;
	}

	console.log('Original AI Response:', responseText);
	const escapedResponse = escapeFn(responseText);
	console.log('Escaped AI Response:', escapedResponse);

	try {
		const messageOptions = { message_id: originalMessageId };
		const sendResult = await sendMessage(env, chatId, escapedResponse, messageOptions);

		if (sendResult) {
			console.log(`Successfully sent AI response to chat ${chatId}`);
		} else {
			console.error(`Failed to send AI response to chat ${chatId}`);
		}
	} catch (error) {
		console.error(`Error sending AI response to chat ${chatId}:`, error);
		throw error;
	}
}
